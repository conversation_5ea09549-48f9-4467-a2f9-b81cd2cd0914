/* player.component.scss - Carousel-based Digital Signage Player */
:host {
  display: block;
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
  background-color: #000;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0 !important;
  padding: 0 !important;
}

.player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
}

// Carousel wrapper
.carousel-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
}

// Carousel container - supports both slide and fade transitions
.carousel-container {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  display: flex;
  transition: transform 0.5s ease-in-out;
  margin: 0 !important;
  padding: 0 !important;

  // Enhanced fade transition support
  &.fade-ready {
    transition: opacity 0.5s ease-in-out;
  }
}

// Individual carousel items
.carousel-item {
  position: relative;
  width: 100vw;
  height: 100vh;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  background: #000;

  // Support for crossfade transitions
  &.crossfade-ready {
    transition: opacity 0.5s ease-in-out;
  }

  // Support for fade transitions
  &.fade-ready {
    transition: opacity 0.5s ease-in-out;
  }
}

// Content items within carousel
.content-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
}

// Image content styling
.image-content {
  .fullscreen-image {
    display: block !important;
    width: 100vw !important;
    height: 100vh !important;
    object-fit: fill !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    max-width: none !important;
    max-height: none !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
  }
}

// Video content styling
.video-content {
  .fullscreen-video {
    display: block !important;
    width: 100vw !important;
    height: 100vh !important;
    object-fit: fill !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    max-width: none !important;
    max-height: none !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
  }
}

// Web content styling
.web-content {
  .fullscreen-iframe {
    display: block !important;
    width: 100vw !important;
    height: 100vh !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
  }
}

// Ticker content styling
.ticker-content {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;

  .ticker-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;

    .ticker-text {
      font-size: 3rem;
      font-weight: 300;
      text-align: center;
      line-height: 1.4;
      animation: fadeInOut 2s ease-in-out infinite alternate;
    }
  }
}

@keyframes fadeInOut {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

// Loading indicator
.loading-indicator {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  background: #000 !important;
  color: white !important;
  z-index: 10;

  .spinner {
    width: 50px !important;
    height: 50px !important;
    border: 5px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50% !important;
    border-top-color: #fff !important;
    animation: spin 1s ease-in-out infinite !important;
    margin-bottom: 1rem !important;
  }

  p {
    font-size: 1.2rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// Subtle preload indicator for background items
.preload-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 5;
  opacity: 0.6;

  .preload-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: rgba(255, 255, 255, 0.8);
    animation: spin 1s ease-in-out infinite;
  }
}

// Error placeholder
.error-placeholder {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  color: #dc3545 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  background: #000 !important;

  .material-icons {
    font-size: 4rem !important;
    margin-bottom: 1rem !important;
  }

  p {
    font-size: 1.2rem !important;
    text-align: center !important;
  }
}

// Fallback content
.fallback-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;

  .material-icons {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.2rem;
    text-align: center;
  }
}

// Carousel indicators
.carousel-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 20;

  .indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transition: background-color 0.3s ease;
    cursor: pointer;

    &.active {
      background-color: rgba(255, 255, 255, 0.9);
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.7);
    }
  }
}

// Error overlay styles - friendly and welcoming
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 !important;
  padding: 0 !important;
}

.error-container {
  text-align: center;
  padding: 3rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  max-width: 80%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .error-icon {
    font-size: 5rem;
    color: #64b5f6;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  }

  .error-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 300;
    color: #ffffff;
  }

  .error-message {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #e3f2fd;
  }

  .error-submessage {
    margin-top: 1rem;
    font-style: italic;
    opacity: 0.8;
    color: #bbdefb;
    font-size: 0.9rem;
  }

  .reload-button {
    margin-top: 2rem;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 0.875rem 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    margin-left: auto;
    margin-right: auto;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    font-size: 0.95rem;

    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// Offline banner
.offline-banner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 193, 7, 0.9);
  color: #212529;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  z-index: 90;
  font-size: 0.875rem;
  font-weight: 500;

  .material-icons {
    font-size: 1rem;
  }
}

// Diagnostic overlay
.diagnostics-overlay {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  z-index: 50;
}

.info-pill {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  backdrop-filter: blur(4px);

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.online {
      background-color: #10b981; // green
    }

    &.offline {
      background-color: #ef4444; // red
    }
  }

  .playlist-name {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Control buttons */
.control-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s, transform 0.2s;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.refresh-button {
  color: #10b981; // green
}

/* Fullscreen styles */
:fullscreen .player-container,
:-webkit-full-screen .player-container,
:-moz-full-screen .player-container,
:-ms-fullscreen .player-container {
  width: 100vw;
  height: 100vh;
}

/* Fix for Safari and iOS */
.player-container:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
}

// Smooth transitions for carousel
.carousel-container {
  &.transitioning {
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// Ensure proper stacking order
.carousel-item {
  z-index: 1;

  &.active {
    z-index: 2;
  }
}